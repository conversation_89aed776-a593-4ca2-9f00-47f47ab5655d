from typing import Literal

from pydantic import Field

from services.base.domain.annotated_types import NonEmptyStr, RoundedFloat
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.event import EventValueLimits
from services.base.domain.schemas.events.exercise.cardio import CardioCategory, CardioFields, CardioIdentifier
from services.base.domain.schemas.events.exercise.exercise import ExerciseCategory, ExerciseFields, ExerciseIdentifier
from services.base.domain.schemas.events.exercise.strength import StrengthCategory, StrengthFields, StrengthIdentifier
from services.data_service.api.models.output.events.event_api_output_base import EventAPIOutputBase


class ExerciseAPIOutput(EventAPIOutputBase, ExerciseIdentifier):
    type: Literal[DataType.Exercise] = Field(alias=ExerciseFields.TYPE)
    category: ExerciseCategory = Field(alias=ExerciseFields.CATEGORY)
    rating: int | None = Field(
        alias=ExerciseFields.RATING,
        ge=EventValueLimits.RATING_MINIMUM_VALUE,
        le=EventValueLimits.RATING_MAXIMUM_VALUE,
    )


class CardioAPIOutput(EventAPIOutputBase, CardioIdentifier):
    type: Literal[DataType.Cardio] = Field(alias=CardioFields.TYPE)
    category: CardioCategory = Field(alias=CardioFields.CATEGORY)
    name: NonEmptyStr = Field(alias=CardioFields.NAME)
    distance: RoundedFloat | None = Field(alias=CardioFields.DISTANCE, ge=0, le=250_000)
    elevation: RoundedFloat | None = Field(alias=CardioFields.ELEVATION, ge=-500, le=8848)
    rating: int | None = Field(
        alias=CardioFields.RATING,
        ge=EventValueLimits.RATING_MINIMUM_VALUE,
        le=EventValueLimits.RATING_MAXIMUM_VALUE,
    )


class StrengthAPIOutput(EventAPIOutputBase, StrengthIdentifier):
    type: Literal[DataType.Strength] = Field(alias=StrengthFields.TYPE)
    category: StrengthCategory = Field(alias=StrengthFields.CATEGORY)
    name: NonEmptyStr = Field(alias=StrengthFields.NAME, max_length=EventValueLimits.MAX_NAME_LENGTH)
    count: int = Field(alias=StrengthFields.COUNT, ge=0, le=1000)
    weight: RoundedFloat | None = Field(alias=StrengthFields.WEIGHT, ge=0, le=500)
    rating: int | None = Field(
        alias=StrengthFields.RATING,
        ge=EventValueLimits.RATING_MINIMUM_VALUE,
        le=EventValueLimits.RATING_MAXIMUM_VALUE,
    )
